const express = require("express");
const mongoose = require("mongoose");
const cors = require("cors");
require("dotenv").config();
const authRoutes = require("./routes/authRoutes");
const mailRoutes = require("./routes/mail");
const freelancerRoutes = require("./routes/freelanceFetchRoute");
const gigsRoutes = require("./routes/gigsRoutes");
const checkoutRoutes = require("./routes/checkoutRoutes");
const orderRoutes = require("./routes/orderRoutes");

const app = express();
const http = require("http").createServer(app);
const PORT = process.env.PORT || 5000;

//-----------Socket IO --------------

const { Server } = require("socket.io");
const io = new Server(http, {
  cors: {
    origin: "http://localhost:5173", // This is our frontend url from where all requests come
    methods: ["GET", "POST"],
  },
});

//--------Socket.io Logic------------

// A Map to track currently online users
// Key: userId, Value: socket.id
let onlineUsers = new Map();

// Listen for new socket connections
io.on("connection", (socket) => {
  console.log("A user connected:", socket.id);

  /**
   * Event: "add-user" 
   * Purpose: Triggered when a user comes online.
   * Stores the mapping of userId to socket.id
   */
  socket.on("add-user", (userId) => {
    onlineUsers.set(userId, socket.id);
  });

  /**
   * Event: "send-msg"
   * Purpose: When one user sends a message to another user.
   * Action: Finds the receiver's socket ID using their userId and emits the message to them.
   */
  socket.on("send-msg", ({ senderId, receiverId, message }) => {
    const receiverSocketId = onlineUsers.get(receiverId);
    if (receiverSocketId) {
      // Send the message to the receiver via their socket
      io.to(receiverSocketId).emit("msg-receive", {
        senderId,
        message,
      });
    }
  });

  /**
   * Event: "disconnect"
   * Purpose: Triggered when a user disconnects from the server.
   * Action: Removes the user from the onlineUsers map.
   */
  socket.on("disconnect", () => {
    for (let [key, value] of onlineUsers.entries()) {
      if (value === socket.id) {
        onlineUsers.delete(key); // Remove disconnected user
      }
    }
    console.log("A user disconnected:", socket.id);
  });
});

//------------Socket IO end----------

// Middleware
app.use(
  cors({
    origin: ["http://localhost:5173", "http://127.0.0.1:5173"],
    methods: ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
    allowedHeaders: ["Content-Type", "Authorization"],
    credentials: true,
  })
);

app.use(express.json());

app.use((req, res, next) => {
  res.setHeader(
    "Content-Security-Policy",
    "default-src 'self'; " +
      "script-src 'self' https://js.stripe.com https://checkout.stripe.com 'unsafe-eval' 'unsafe-inline' blob:; " +
      "style-src 'self' https://checkout.stripe.com 'unsafe-inline'; " +
      "font-src 'self' data:; " +
      "frame-src 'self' https://js.stripe.com https://hooks.stripe.com https://checkout.stripe.com; " +
      "connect-src 'self' https://api.stripe.com https://maps.googleapis.com; " +
      "img-src 'self' https://*.stripe.com data: blob:;"
  );
  next();
});
// Routes
app.use("/api/auth", authRoutes);
app.use("/api/mail", mailRoutes);
app.use("/api/freelancers", freelancerRoutes);
app.use("/api/gigs", gigsRoutes);
app.use("/api/checkout", checkoutRoutes);
app.use("/api/orders", orderRoutes);

// Database connection
mongoose
  .connect(process.env.MONGODB_URI, {
    useNewUrlParser: true,
    useUnifiedTopology: true,
  })
  .then(() => console.log("MongoDB connected"))
  .catch((err) => console.error("MongoDB connection error:", err));

// Start the server
http.listen(PORT, () => {
  console.log(`Server is running on http://localhost:${PORT}`);
});
