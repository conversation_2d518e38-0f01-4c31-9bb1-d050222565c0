import React, { useState } from "react";

const dummyClients = [
  { id: "c1", name: "Client A" },
  { id: "c2", name: "Client B" },
  { id: "c3", name: "Client C" },
];

const FreelancerChat = () => {
  const [selectedClient, setSelectedClient] = useState(dummyClients[0]);
  const [messages, setMessages] = useState([]);
  const [input, setInput] = useState("");

  const handleSend = () => {
    if (input.trim()) {
      setMessages([...messages, { sender: "freelancer", text: input }]);
      setInput("");
    }
  };

  return (
    <div className="flex h-screen">
      {/* Left Panel: Client List */}
      <div className="w-1/4 border-r p-4 bg-gray-100">
        <h2 className="text-xl font-bold mb-4">Clients</h2>
        {dummyClients.map((client) => (
          <div
            key={client.id}
            onClick={() => setSelectedClient(client)}
            className={`p-2 cursor-pointer rounded ${
              selectedClient.id === client.id ? "bg-blue-500 text-white" : ""
            }`}
          >
            {client.name}
          </div>
        ))}
      </div>

      {/* Right Panel: Chat Box */}
      <div className="w-3/4 flex flex-col">
        {/* Chat Header */}
        <div className="p-4 border-b font-semibold bg-gray-50">
          Chatting with: {selectedClient.name}
        </div>

        {/* Messages */}
        <div className="flex-1 p-4 overflow-y-auto bg-white">
          {messages.map((msg, idx) => (
            <div
              key={idx}
              className={`mb-2 max-w-xs p-2 rounded ${
                msg.sender === "freelancer"
                  ? "ml-auto bg-blue-200"
                  : "mr-auto bg-gray-300"
              }`}
            >
              {msg.text}
            </div>
          ))}
        </div>

        {/* Input */}
        <div className="p-4 border-t flex gap-2">
          <input
            type="text"
            className="flex-1 border p-2 rounded"
            placeholder="Type a message"
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyDown={(e) => e.key === "Enter" && handleSend()}
          />
          <button
            onClick={handleSend}
            className="bg-blue-500 text-white px-4 py-2 rounded"
          >
            Send
          </button>
        </div>
      </div>
    </div>
  );
};

export default FreelancerChat;
