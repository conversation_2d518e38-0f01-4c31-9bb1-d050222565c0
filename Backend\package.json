{"name": "backend", "version": "1.0.0", "main": "server.js", "scripts": {"start": "node index.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "Backend for the Freelancer Clone project", "dependencies": {"bcrypt": "^6.0.0", "bcryptjs": "^3.0.2", "cloudinary": "^1.41.3", "cors": "^2.8.5", "dotenv": "^17.0.1", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.1", "multer": "^2.0.1", "multer-storage-cloudinary": "^4.0.0", "nodemailer": "^7.0.5", "socket.io": "^4.8.1", "stripe": "^18.3.0"}, "devDependencies": {"nodemon": "^2.0.22"}}