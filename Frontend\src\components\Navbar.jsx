import React, { useState } from 'react'

const Navbar = () => {
  const [menuOpen, setMenuOpen] = useState(false)

  return (
    <>
      <nav className="p-4 flex items-center justify-between border-b shadow-md fixed w-full z-10">
        <h1 className="text-3xl font-extrabold tracking-tight flex items-center gap-1">
          Giglyy<span className="text-green-500">.</span>
        </h1>
        {/* Desktop Menu */}
        <ul className="hidden md:flex gap-8 font-medium items-center">
          <li className="hover:text-green-500 transition-colors cursor-pointer">Home</li>
          <li className="hover:text-green-500 transition-colors cursor-pointer">Services</li>
          <li className="hover:text-green-500 transition-colors cursor-pointer">Contact</li>
          <li>
            <button className="bg-green-500 hover:bg-green-600 transition-colors text-white px-4 py-2 rounded-md shadow">
              Login
            </button>
          </li>
        </ul>
        {/* Hamburger Icon */}
        <button
          className="md:hidden flex flex-col gap-1 focus:outline-none"
          onClick={() => setMenuOpen(!menuOpen)}
          aria-label="Toggle menu"
        >
          <span className={`block h-1 w-6 bg-green-500 rounded transition-all ${menuOpen ? "rotate-45 translate-y-2" : ""}`}></span>
          <span className={`block h-1 w-6 bg-green-500 rounded transition-all ${menuOpen ? "opacity-0" : ""}`}></span>
          <span className={`block h-1 w-6 bg-green-500 rounded transition-all ${menuOpen ? "-rotate-45 -translate-y-2" : ""}`}></span>
        </button>
        {/* Mobile Menu */}
        {menuOpen && (
          <ul className="absolute top-16 right-4 border rounded-lg shadow-lg flex flex-col gap-4 p-6 font-medium items-start md:hidden animate-fade-in">
            <li className="hover:text-green-500 transition-colors cursor-pointer">Home</li>
            <li className="hover:text-green-500 transition-colors cursor-pointer">Services</li>
            <li className="hover:text-green-500 transition-colors cursor-pointer">Contact</li>
            <li>
              <button className="bg-green-500 hover:bg-green-600 transition-colors text-white px-4 py-2 rounded-md shadow">
                Login
              </button>
            </li>
          </ul>
        )}
      </nav>
      {/* Spacer for fixed navbar */}
      <div className="h-16 md:h-20"></div>
    </>
  )
}

export default Navbar